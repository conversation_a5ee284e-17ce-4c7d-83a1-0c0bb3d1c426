import { useClockInMutation, useShiftQuery } from "@/generated/graphql";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useRef, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Button,
  Pressable,
  ScrollView,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withDelay,
  Easing,
} from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";
import { Colors, hexToRGBA } from "@/constants/Colors";
import { StatusBar } from "@/components/ui/StatusBar";
import { format } from "date-fns";
import { CameraView, useCameraPermissions } from "expo-camera";
import { LinearGradient } from "expo-linear-gradient";

export default function ShiftDetails() {
  const router = useRouter();
  const ref = useRef<CameraView>(null);

  // Enhanced animation values
  const cardOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(0.95);
  const headerOpacity = useSharedValue(0);
  const cardsTranslateY = useSharedValue(30);
  const attendanceCardScale = useSharedValue(0.9);

  const [isClockedIn, setIsClockedIn] = useState(false);
  const [clockInTime, setClockInTime] = useState<Date | null>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();

  React.useEffect(() => {
    // Staggered entrance animations
    headerOpacity.value = withTiming(1, { duration: 400 });

    cardOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
    cardsTranslateY.value = withDelay(
      200,
      withSpring(0, { damping: 15, stiffness: 100 })
    );

    buttonScale.value = withDelay(
      400,
      withTiming(1, {
        duration: 500,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      })
    );

    attendanceCardScale.value = withDelay(
      600,
      withSpring(1, { damping: 12, stiffness: 120 })
    );
  }, []);
  const { shiftId } = useLocalSearchParams<{ shiftId: string }>();
  const { data: shift } = useShiftQuery({ shiftId });
  const { mutateAsync: clockIn } = useClockInMutation();

  // Enhanced animated styles
  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: buttonScale.value }],
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));

  const cardsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ translateY: cardsTranslateY.value }],
  }));

  const attendanceAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: attendanceCardScale.value }],
  }));

  // Handle clock in
  const handleClockIn = async (base64Img: string) => {
    try {
      const date = new Date();
      const locationId = shift?.shift.location?.id;
      const shiftId = shift?.shift.id;

      if (!shiftId || !locationId)
        throw new Error("Shift/location ID is missing");

      await clockIn({
        clockInInput: { base64Img, date, locationId, shiftId },
      });

      setClockInTime(date);
      setIsClockedIn(true);
      setShowCamera(false);
    } catch (error) {
      console.error("Clock in failed", error);
      setShowCamera(false);
    }
  };

  // Handle clock out
  const handleClockOut = async () => {
    try {
      if (!shiftId) throw new Error("Shift ID is missing");

      // TODO: Implement actual clock out mutation
      setIsClockedIn(false);
    } catch (error) {
      console.error("Clock out failed", error);
    }
  };

  const formatTime = (date: Date | null): string => {
    if (!date) return "";
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const handleBackPress = () => {
    router.back();
  };

  const takePicture = async () => {
    try {
      const photo = await ref.current?.takePictureAsync({ base64: true });
      if (photo?.base64) {
        const base64Img = `data:image/jpeg;base64,${photo.base64}`;
        await handleClockIn(base64Img);
      }
    } catch (err) {
      console.error("Failed to take picture", err);
    }
  };

  if (!permission) {
    return null;
  }

  if (!permission?.granted) {
    // Camera permissions are not granted yet.
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.message}>
          We need your permission to show the camera
        </Text>
        <Button onPress={requestPermission} title="grant permission" />
      </View>
    );
  }

  if (showCamera) {
    return (
      <View style={{ flex: 1 }}>
        <CameraView
          style={styles.camera}
          ref={ref}
          facing="front"
          mute={false}
          responsiveOrientationWhenOrientationLocked
        >
          <TouchableOpacity
            onPress={() => setShowCamera(false)}
            style={{ position: "absolute", top: 50, left: 20, zIndex: 99 }}
          >
            <Ionicons name="close" size={32} color="white" />
          </TouchableOpacity>
          <View style={styles.shutterContainer}>
            <Pressable onPress={takePicture}>
              {({ pressed }) => (
                <View
                  style={[styles.shutterBtn, { opacity: pressed ? 0.5 : 1 }]}
                >
                  <View
                    style={[
                      styles.shutterBtnInner,
                      { backgroundColor: "white" },
                    ]}
                  />
                </View>
              )}
            </Pressable>
          </View>
        </CameraView>
      </View>
    );
  }

  //   TODO: Implement shift details
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />
      {/* Enhanced Header with Gradient */}
      <Animated.View style={[styles.headerContainer, headerAnimatedStyle]}>
        <LinearGradient
          colors={[Colors.primary, Colors.primaryDark]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
            <Ionicons name="chevron-back" size={28} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Shift Details</Text>
        </LinearGradient>
        <View style={styles.headerAccent} />
      </Animated.View>

      <ScrollView
        style={styles.detailsContainer}
        contentContainerStyle={styles.scrollContentCompact}
        showsVerticalScrollIndicator={false}
      >
        {/* Enhanced Location Information Section */}
        <View style={styles.infoSectionCompact}>
          <LinearGradient
            colors={[Colors.primary, Colors.primaryLight]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.sectionHeaderGradient}
          >
            <View style={styles.sectionHeaderContent}>
              <View style={styles.sectionIconContainer}>
                <Ionicons name="location" size={18} color={Colors.white} />
              </View>
              <Text style={styles.sectionTitleGradient}>Location Details</Text>
            </View>
          </LinearGradient>

          <View style={styles.infoListCompact}>
            <View style={styles.infoRowCompact}>
              <Ionicons
                name="business"
                size={14}
                color={Colors.primary}
                style={styles.infoIconCompact}
              />
              <View style={styles.infoContentCompact}>
                <Text style={styles.infoLabelCompact}>Location Name</Text>
                <Text style={styles.infoValueCompact}>
                  {shift?.shift.location?.name || "Not specified"}
                </Text>
              </View>
            </View>

            {shift?.shift.location?.address && (
              <View style={styles.infoRowCompact}>
                <Ionicons
                  name="map"
                  size={14}
                  color={Colors.primary}
                  style={styles.infoIconCompact}
                />
                <View style={styles.infoContentCompact}>
                  <Text style={styles.infoLabelCompact}>Address</Text>
                  <Text style={styles.infoValueCompact}>
                    {shift.shift.location.address}
                  </Text>
                </View>
              </View>
            )}

            {shift?.shift.location?.emergencyContact && (
              <View style={styles.infoRowCompact}>
                <Ionicons
                  name="call"
                  size={14}
                  color={Colors.error}
                  style={styles.infoIconCompact}
                />
                <View style={styles.infoContentCompact}>
                  <Text style={styles.infoLabelCompact}>Emergency Contact</Text>
                  <Text
                    style={[styles.infoValueCompact, { color: Colors.error }]}
                  >
                    {shift.shift.location.emergencyContact}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Enhanced Assigned Guards Section */}
        <Animated.View
          style={[
            styles.infoSectionCompact,
            styles.guardsSection,
            cardsAnimatedStyle,
          ]}
        >
          <LinearGradient
            colors={[Colors.secondary, Colors.secondaryLight]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.sectionHeaderGradient}
          >
            <View style={styles.sectionHeaderContent}>
              <View style={styles.sectionIconContainer}>
                <Ionicons name="people" size={18} color={Colors.white} />
              </View>
              <Text style={styles.sectionTitleGradient}>Assigned Guards</Text>
            </View>
          </LinearGradient>

          <View style={styles.infoListCompact}>
            <View style={styles.infoRowCompact}>
              <Ionicons
                name="person"
                size={14}
                color={Colors.secondary}
                style={styles.infoIconCompact}
              />
              <View style={styles.infoContentCompact}>
                <Text style={styles.infoLabelCompact}>Guards on Duty</Text>
                <Text style={styles.infoValueCompact}>
                  {shift?.shift.users
                    ?.map((user) => user.fullname)
                    .join(", ") || "No guards assigned"}
                </Text>
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Enhanced Shift Timing Section */}
        <Animated.View
          style={[
            styles.infoSectionCompact,
            styles.scheduleSection,
            cardsAnimatedStyle,
          ]}
        >
          <LinearGradient
            colors={[Colors.warning, "#F2C94C"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.sectionHeaderGradient}
          >
            <View style={styles.sectionHeaderContent}>
              <View style={styles.sectionIconContainer}>
                <Ionicons name="time" size={18} color={Colors.white} />
              </View>
              <Text style={styles.sectionTitleGradient}>Shift Schedule</Text>
            </View>
          </LinearGradient>

          <View style={styles.infoListCompact}>
            <View style={styles.infoRowCompact}>
              <Ionicons
                name="play"
                size={14}
                color={Colors.success}
                style={styles.infoIconCompact}
              />
              <View style={styles.infoContentCompact}>
                <Text style={styles.infoLabelCompact}>Start Time</Text>
                <Text style={styles.infoValueCompact}>
                  {shift?.shift.startDateTime
                    ? format(
                        new Date(shift.shift.startDateTime),
                        "EEEE, dd MMM yyyy 'at' hh:mm a"
                      )
                    : "Not specified"}
                </Text>
              </View>
            </View>

            <View style={styles.infoRowCompact}>
              <Ionicons
                name="stop"
                size={14}
                color={Colors.error}
                style={styles.infoIconCompact}
              />
              <View style={styles.infoContentCompact}>
                <Text style={styles.infoLabelCompact}>End Time</Text>
                <Text style={styles.infoValueCompact}>
                  {shift?.shift.endDateTime
                    ? format(
                        new Date(shift.shift.endDateTime),
                        "EEEE, dd MMM yyyy 'at' hh:mm a"
                      )
                    : "Not specified"}
                </Text>
              </View>
            </View>

            <View style={styles.infoRowCompact}>
              <Ionicons
                name="timer"
                size={14}
                color={Colors.warning}
                style={styles.infoIconCompact}
              />
              <View style={styles.infoContentCompact}>
                <Text style={styles.infoLabelCompact}>Duration</Text>
                <Text style={styles.infoValueCompact}>
                  {shift?.shift.startDateTime && shift?.shift.endDateTime
                    ? `${Math.round(
                        (new Date(shift.shift.endDateTime).getTime() -
                          new Date(shift.shift.startDateTime).getTime()) /
                          (1000 * 60 * 60)
                      )} hours`
                    : "Not calculated"}
                </Text>
              </View>
            </View>
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  detailsContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },

  // Enhanced Header Styles
  headerAccent: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: Colors.primaryLight,
    shadowColor: Colors.primaryLight,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
  },

  // Enhanced Attendance Section
  attendanceSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: Colors.white,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  attendanceTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  statusBadge: {
    backgroundColor: Colors.success,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: "600",
  },

  // Enhanced Clock Buttons
  clockButtonsWrapper: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  buttonIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255,255,255,0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  buttonTextContainer: {
    flex: 1,
  },
  buttonSubtext: {
    fontSize: 12,
    color: "rgba(255,255,255,0.8)",
    marginTop: 2,
  },

  // Enhanced Card Styles
  enhancedInfoCard: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    marginBottom: 20,
    overflow: "hidden",
    elevation: 2,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  locationCard: {
    // Specific styles for location card if needed
  },
  guardsCard: {
    // Specific styles for guards card if needed
  },
  scheduleCard: {
    // Specific styles for schedule card if needed
  },
  attendanceCard: {
    // Specific styles for attendance card if needed
  },
  cardGradientHeader: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  cardIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255,255,255,0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  cardTitleWhite: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.white,
    flex: 1,
  },
  cardContent: {
    padding: 20,
  },
  infoIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.background,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },

  // Original styles (keeping for compatibility)
  infoCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
    marginLeft: 8,
  },
  infoSection: {
    gap: 12,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.textSecondary,
    textTransform: "uppercase",
    letterSpacing: 0.5,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 15,
    fontWeight: "500",
    color: Colors.text,
    lineHeight: 20,
  },
  camera: {
    flex: 1,
    width: "100%",
  },
  shutterContainer: {
    position: "absolute",
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  shutterBtn: {
    backgroundColor: "transparent",
    borderWidth: 5,
    borderColor: "white",
    width: 85,
    height: 85,
    borderRadius: 45,
    alignItems: "center",
    justifyContent: "center",
  },
  shutterBtnInner: {
    width: 70,
    height: 70,
    borderRadius: 50,
  },
  permissionContainer: { flex: 1, justifyContent: "center" },
  message: {
    textAlign: "center",
    paddingBottom: 10,
  },

  markAttendance: {
    fontSize: 20,
    fontWeight: "700",
    color: Colors.text,
    marginLeft: 12,
  },
  headerContainer: {
    position: "relative",
    overflow: "hidden",
  },
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.white,
    flex: 1,
  },
  card: {
    marginHorizontal: 16,
    marginVertical: 12,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 16,
  },
  gridContainer: {
    marginBottom: 20,
  },
  dayRow: {
    marginBottom: 12,
  },
  dayRecords: {
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
  },
  statusBox: {
    width: 24,
    height: 24,
    borderRadius: 4,
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  summaryItem: {
    flex: 1,
    alignItems: "center",
  },
  summaryLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  summaryIndicator: {
    width: 16,
    height: 4,
    borderRadius: 2,
    marginRight: 6,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
  },
  // Enhanced Clock in/out button styles
  clockButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 16,
  },
  clockButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 16,
    minHeight: 80,
    elevation: 3,
    borderWidth: 0,
  },
  clockInButton: {
    backgroundColor: Colors.primary,
  },
  clockOutButton: {
    backgroundColor: Colors.error,
  },
  clockButtonDisabled: {
    backgroundColor: Colors.lightGray,
    elevation: 0,
    opacity: 0.6,
  },
  clockButtonText: {
    fontWeight: "700",
    fontSize: 16,
    color: Colors.white,
  },
  clockButtonTextDisabled: {
    color: Colors.textSecondary,
  },
  clockInButtonText: {
    color: Colors.white,
  },
  clockOutButtonText: {
    color: Colors.white,
  },
  // Work summary styles
  workSummaryContainer: {
    marginTop: 16,
  },
  workSummaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  workSummaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  workSummaryValue: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: "500",
  },
  totalHoursRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    borderBottomWidth: 0,
  },
  totalHoursLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.text,
  },
  totalHoursValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.primary,
  },

  // Compact Design Styles
  scrollContentCompact: {
    padding: 12,
    paddingBottom: 24,
  },

  // Enhanced Compact Attendance Section
  attendanceSectionCompact: {
    backgroundColor: Colors.white,
    marginHorizontal: 12,
    marginTop: 12,
    borderRadius: 12,
    elevation: 2,
    borderWidth: 1,
    borderColor: hexToRGBA(Colors.border, 0.3),
    overflow: "hidden",
  },
  attendanceHeaderGradient: {
    paddingHorizontal: 12,
    paddingVertical: 14,
  },
  attendanceIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: hexToRGBA(Colors.primary, 0.1),
    alignItems: "center",
    justifyContent: "center",
    marginRight: 10,
  },
  attendanceTitleContainerCompact: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  markAttendanceCompact: {
    fontSize: 17,
    fontWeight: "700",
    color: Colors.text,
    flex: 1,
  },
  statusBadgeCompact: {
    backgroundColor: Colors.success,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    elevation: 1,
    shadowColor: Colors.success,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  statusBadgeTextCompact: {
    color: Colors.white,
    fontSize: 11,
    fontWeight: "700",
    textShadowColor: "rgba(0,0,0,0.3)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },

  // Modern Clock Buttons - Horizontal Layout
  modernClockButtonsWrapper: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  modernClockButtonsContainer: {
    flexDirection: "row",
    gap: 0,
    borderRadius: 16,
    elevation: 4,
    shadowColor: Colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    overflow: "hidden",
  },
  modernClockButton: {
    flex: 1,
    overflow: "hidden",
  },
  modernClockButtonLeft: {
    borderTopLeftRadius: 16,
    borderBottomLeftRadius: 16,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  modernClockButtonRight: {
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 16,
    borderBottomRightRadius: 16,
  },
  modernClockButtonDisabled: {
    elevation: 0,
    shadowOpacity: 0,
  },
  modernButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 20,
    paddingHorizontal: 16,
    minHeight: 80,
    position: "relative",
    flex: 1,
  },

  // Status Indicator
  modernButtonStatusIndicator: {
    position: "absolute",
    left: 0,
    top: 0,
    bottom: 0,
    width: 4,
  },

  // Icon Container
  modernButtonIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255,255,255,0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
    elevation: 2,
  },
  modernButtonIconContainerSuccess: {
    backgroundColor: "rgba(39, 174, 96, 0.2)",
  },
  modernButtonIconContainerDisabled: {
    backgroundColor: "rgba(255,255,255,0.05)",
    elevation: 0,
  },

  // Content Section
  modernButtonContent: {
    flex: 1,
    justifyContent: "center",
  },
  modernButtonTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: Colors.white,
    marginBottom: 3,
    textShadowColor: "rgba(0,0,0,0.3)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modernButtonTitleDisabled: {
    color: Colors.textSecondary,
    textShadowColor: "transparent",
  },
  modernButtonSubtitle: {
    fontSize: 12,
    color: "rgba(255,255,255,0.9)",
    fontWeight: "500",
    marginBottom: 6,
  },
  modernButtonSubtitleDisabled: {
    color: Colors.textLight,
  },

  // Badges
  modernButtonBadge: {
    backgroundColor: "rgba(255,255,255,0.2)",
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 8,
    alignSelf: "flex-start",
  },
  modernButtonBadgeText: {
    fontSize: 10,
    fontWeight: "700",
    color: Colors.white,
    letterSpacing: 0.5,
  },
  modernButtonBadgeDisabled: {
    backgroundColor: "rgba(0,0,0,0.1)",
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 8,
    alignSelf: "flex-start",
  },
  modernButtonBadgeTextDisabled: {
    fontSize: 10,
    fontWeight: "700",
    color: Colors.textLight,
    letterSpacing: 0.5,
  },

  // Arrow Indicator
  modernButtonArrow: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: "rgba(255,255,255,0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 6,
  },

  // Enhanced Compact Info Sections
  infoSectionCompact: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginBottom: 12,
    overflow: "hidden",
    elevation: 2,
    borderWidth: 1,
    borderColor: hexToRGBA(Colors.border, 0.3),
    shadowColor: Colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
  },
  sectionHeaderCompact: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: Colors.background,
  },
  sectionTitleCompact: {
    fontSize: 15,
    fontWeight: "600",
    color: Colors.text,
    marginLeft: 8,
  },
  infoListCompact: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.white,
  },
  infoRowCompact: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
    paddingBottom: 8,
  },
  infoIconCompact: {
    marginRight: 12,
    marginTop: 2,
    width: 20,
    textAlign: "center",
  },
  infoContentCompact: {
    flex: 1,
  },
  infoLabelCompact: {
    fontSize: 11,
    fontWeight: "700",
    color: Colors.textSecondary,
    textTransform: "uppercase",
    letterSpacing: 0.8,
    marginBottom: 4,
  },
  infoValueCompact: {
    fontSize: 15,
    fontWeight: "600",
    color: Colors.text,
    lineHeight: 20,
  },

  // Section-specific styles
  guardsSection: {
    // Specific styles for guards section if needed
  },
  scheduleSection: {
    // Specific styles for schedule section if needed
  },

  // Enhanced Gradient Header Styles
  sectionHeaderGradient: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  sectionHeaderContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  sectionIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255,255,255,0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 10,
  },
  sectionTitleGradient: {
    fontSize: 15,
    fontWeight: "700",
    color: Colors.white,
    flex: 1,
    textShadowColor: "rgba(0,0,0,0.3)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
